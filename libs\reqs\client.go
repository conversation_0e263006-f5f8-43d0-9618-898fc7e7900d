package reqs

import (
	"fmt"

	"github.com/imroc/req/v3"
	"github.com/spf13/viper"
)

type Client interface {
	Request() Request
	Config() *Config
	Copy() Client
	SetBaseUrl(s string) Client
}

type client struct {
	client *req.Client
	config *Config
}

func NewClient(key string) Client {
	var config Config
	if err := viper.Unmarshal<PERSON>ey(key, &config); err != nil {
		panic(fmt.Errorf("failed to new req client: %v", err))
	}

	c := client{
		client: req.NewClient(),
		config: &config,
	}

	if config.EnableInsecureSkipVerify {
		c.client.EnableInsecureSkipVerify()
	}

	c.client.BaseURL = config.BaseUrl
	if config.Timeout != 0 {
		c.client.SetTimeout(config.Timeout)
	}

	return &c
}

func NewClientWithConfig(config Config) Client {
	c := client{
		client: req.NewClient(),
		config: &config,
	}

	if config.EnableInsecureSkipVerify {
		c.client.EnableInsecureSkipVerify()
	}

	c.client.BaseURL = config.BaseUrl
	if config.Timeout != 0 {
		c.client.SetTimeout(config.Timeout)
	}

	return &c
}

func (c *client) Request() Request {
	request := request{
		request: c.client.NewRequest(),
		config:  c.config,
	}

	return &request
}

func (c *client) Config() *Config {
	return c.config
}

func (c *client) Copy() Client {
	cfg := *c.config
	cli := *c.client

	return &client{
		client: &cli,
		config: &cfg,
	}
}

func (c *client) SetBaseUrl(s string) Client {
	c.client.BaseURL = s
	c.config.BaseUrl = s

	return c
}
