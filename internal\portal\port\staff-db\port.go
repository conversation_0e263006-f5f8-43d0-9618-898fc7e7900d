package staffdb

import (
	"digital-transformation-api/internal/enum/staff"
	"digital-transformation-api/internal/portal/domain"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type Port interface {
	Create(request *CreateRequest, rctx *contexts.RouteContext, l logger.Logger) (*CreateResponse, errs.Error)
	GetByID(request *GetByIDRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByIDResponse, errs.Error)
	GetByEmail(request *GetByEmailRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByEmailResponse, errs.Error)
	GetByEmployeeIDAndPhone(request *GetByEmployeeIDAndPhoneRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByEmployeeIDAndPhoneResponse, errs.Error)
	GetByUserTokenOrStaffId(request *GetByUserTokenOrStaffIdRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByUserTokenOrStaffIdResponse, errs.Error)
	Update(request *UpdateRequest, rctx *contexts.RouteContext, l logger.Logger) (*UpdateResponse, errs.Error)
	Delete(request *DeleteRequest, rctx *contexts.RouteContext, l logger.Logger) (*DeleteResponse, errs.Error)
	List(request *ListRequest, rctx *contexts.RouteContext, l logger.Logger) (*ListResponse, errs.Error)
	Count(request *CountRequest, rctx *contexts.RouteContext, l logger.Logger) (*CountResponse, errs.Error)
}

type CreateRequest struct {
	Staff *domain.Staff `json:"staff"`
}

type CreateResponse struct {
	Staff *domain.Staff `json:"staff"`
}

type GetByIDRequest struct {
	ID string `json:"id"`
}

type GetByIDResponse struct {
	Staff *domain.Staff `json:"staff"`
}

type GetByEmailRequest struct {
	Email string `json:"email"`
}

type GetByEmailResponse struct {
	Staff *domain.Staff `json:"staff"`
}

type GetByEmployeeIDAndPhoneRequest struct {
	EmployeeID string `json:"employee_id"`
	Phone      string `json:"phone"`
}

type GetByUserTokenOrStaffIdRequest struct {
	UserToken string `json:"user_token"`
	StaffID   string `json:"staff_id"`
}

type GetByEmployeeIDAndPhoneResponse struct {
	Staff *domain.Staff `json:"staff"`
}

type GetByUserTokenOrStaffIdResponse struct {
	Staff *domain.StaffToken `json:"staffToken"`
}

type UpdateRequest struct {
	Staff *domain.Staff `json:"staff"`
}

type UpdateResponse struct {
	Staff *domain.Staff `json:"staff"`
}

type DeleteRequest struct {
	ID string `json:"id"`
}

type DeleteResponse struct {
	Success bool `json:"success"`
}

type ListRequest struct {
	Page     int64         `json:"page"`
	PageSize int64         `json:"page_size"`
	Status   *staff.Status `json:"status"`
	ShopID   *string       `json:"shop_id"`
	AreaID   *string       `json:"area_id"`
	Search   *string       `json:"search"`
}

type ListResponse struct {
	Staff []domain.Staff `json:"staff"`
}

type CountRequest struct {
	Status *staff.Status `json:"status"`
	ShopID *string       `json:"shop_id"`
	AreaID *string       `json:"area_id"`
	Search *string       `json:"search"`
}

type CountResponse struct {
	Count int64 `json:"count"`
}
