package logs

import (
	"log/slog"
	"os"

	"github.com/google/uuid"
)

var (
	SL *slog.Logger = slog.New(
		NewJsonHandler(
			os.Stdout,
			&slog.HandlerOptions{
				Level: slog.LevelInfo,
			},
		),
	)

	L = New(map[string]any{
		TraceID: uuid.NewString(),
		SpanID:  uuid.NewString(),
	})
)

const (
	Message     = "message"
	MaskingChar = "*"
)

const (
	TraceID = "traceId"
	SpanID  = "spanId"
)

var MaskingList = map[string]bool{
	"username":     true,
	"password":     true,
	"email":        true,
	"firstName":    true,
	"lastName":     true,
	"accessToken":  true,
	"refreshToken": true,
}
