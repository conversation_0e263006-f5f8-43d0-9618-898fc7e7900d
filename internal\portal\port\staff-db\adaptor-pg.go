package staffdb

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"strings"
	"time"

	"digital-transformation-api/internal/portal/domain"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gorms"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/logs"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type adaptorPG struct {
	db *gorm.DB
}

func NewAdaptorPG(db *gorm.DB) Port {
	return &adaptorPG{
		db: db,
	}
}

func (a *adaptorPG) Create(request *CreateRequest, rctx *contexts.RouteContext, l logger.Logger) (*CreateResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	// Generate ID and timestamps
	request.Staff.ID = uuid.New().String()
	request.Staff.CreatedAt = time.Now()

	// Hash password with salt
	salt := generateSalt()
	hashedPassword := hashPassword(request.Staff.Password, salt)
	request.Staff.Password = hashedPassword
	request.Staff.Salt = salt

	if err := tx.Create(request.Staff).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			l.Errorf("staff email already exists: %v", err)
			return nil, errs.NewBusinessError("40001")
		}
		l.Errorf("failed to create staff: %v", err)
		return nil, errs.NewInternalError()
	}

	return &CreateResponse{
		Staff: request.Staff,
	}, nil
}

func (a *adaptorPG) GetByID(request *GetByIDRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByIDResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	var staff domain.Staff
	if err := tx.Where("id = ?", request.ID).First(&staff).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			l.Errorf("staff not found: %v", err)
			return nil, errs.NewBusinessError("40404")
		}
		l.Errorf("failed to get staff: %v", err)
		return nil, errs.NewInternalError()
	}

	return &GetByIDResponse{
		Staff: &staff,
	}, nil
}

func (a *adaptorPG) GetByEmail(request *GetByEmailRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByEmailResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	var staff domain.Staff
	if err := tx.Where("email = ?", request.Email).First(&staff).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return &GetByEmailResponse{Staff: nil}, nil
		}
		l.Errorf("failed to get staff by email: %v", err)
		return nil, errs.NewInternalError()
	}

	return &GetByEmailResponse{
		Staff: &staff,
	}, nil
}

func (a *adaptorPG) GetByEmployeeIDAndPhone(request *GetByEmployeeIDAndPhoneRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByEmployeeIDAndPhoneResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	var staff domain.Staff
	if err := tx.Where("employee_id = ? AND phone = ?", request.EmployeeID, request.Phone).First(&staff).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return &GetByEmployeeIDAndPhoneResponse{Staff: nil}, nil
		}
		l.Errorf("failed to get staff by employee_id and phone: %v", err)
		return nil, errs.NewInternalError()
	}

	return &GetByEmployeeIDAndPhoneResponse{
		Staff: &staff,
	}, nil
}

func (a *adaptorPG) GetByUserTokenOrStaffId(request *GetByUserTokenOrStaffIdRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByUserTokenOrStaffIdResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	var staffToken domain.StaffToken
	query := tx.Where("token = ?", request.UserToken).Or("staff_id = ?", request.StaffID)
	if err := query.First(&staffToken).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return &GetByUserTokenOrStaffIdResponse{StaffToken: nil}, nil
		}
		l.Errorf("failed to get staff token: %v", err)
		return nil, errs.NewInternalError()
	}

	return &GetByUserTokenOrStaffIdResponse{
		StaffToken: &staffToken,
	}, nil
}

func (a *adaptorPG) Update(request *UpdateRequest, rctx *contexts.RouteContext, l logger.Logger) (*UpdateResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	// Set updated timestamp
	now := time.Now()
	request.Staff.UpdatedAt = &now

	if err := tx.Where("id = ?", request.Staff.ID).Updates(request.Staff).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			l.Errorf("staff email already exists: %v", err)
			return nil, errs.NewBusinessError("40001")
		}
		l.Errorf("failed to update staff: %v", err)
		return nil, errs.NewInternalError()
	}

	// Get updated staff
	var updatedStaff domain.Staff
	if err := tx.Where("id = ?", request.Staff.ID).First(&updatedStaff).Error; err != nil {
		l.Errorf("failed to get updated staff: %v", err)
		return nil, errs.NewInternalError()
	}

	return &UpdateResponse{
		Staff: &updatedStaff,
	}, nil
}

func (a *adaptorPG) Delete(request *DeleteRequest, rctx *contexts.RouteContext, l logger.Logger) (*DeleteResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	result := tx.Where("id = ?", request.ID).Delete(&domain.Staff{})
	if result.Error != nil {
		l.Errorf("failed to delete staff: %v", result.Error)
		return nil, errs.NewInternalError()
	}

	if result.RowsAffected == 0 {
		l.Errorf("staff not found for deletion")
		return nil, errs.NewBusinessError("40404")
	}

	return &DeleteResponse{
		Success: true,
	}, nil
}

func (a *adaptorPG) List(request *ListRequest, rctx *contexts.RouteContext, l logger.Logger) (*ListResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	query := tx.Model(&domain.Staff{})

	// Apply filters
	query = gorms.Equal(query, "status", request.Status)
	query = gorms.Equal(query, "shop_id", request.ShopID)
	query = gorms.Equal(query, "area_id", request.AreaID)

	// Apply search
	if request.Search != nil && *request.Search != "" {
		searchTerm := "%" + *request.Search + "%"
		query = query.Where("firstname ILIKE ? OR lastname ILIKE ? OR email ILIKE ?", searchTerm, searchTerm, searchTerm)
	}

	// Apply pagination
	query = gorms.Offset(query, request.Page, request.PageSize)
	query = gorms.Limit(query, request.PageSize)

	var staff []domain.Staff
	if err := query.Find(&staff).Error; err != nil {
		l.Errorf("failed to list staff: %v", err)
		return nil, errs.NewInternalError()
	}

	return &ListResponse{
		Staff: staff,
	}, nil
}

func (a *adaptorPG) Count(request *CountRequest, rctx *contexts.RouteContext, l logger.Logger) (*CountResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	query := tx.Model(&domain.Staff{})

	// Apply filters
	query = gorms.Equal(query, "status", request.Status)
	query = gorms.Equal(query, "shop_id", request.ShopID)
	query = gorms.Equal(query, "area_id", request.AreaID)

	// Apply search
	if request.Search != nil && *request.Search != "" {
		searchTerm := "%" + *request.Search + "%"
		query = query.Where("firstname ILIKE ? OR lastname ILIKE ? OR email ILIKE ?", searchTerm, searchTerm, searchTerm)
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		l.Errorf("failed to count staff: %v", err)
		return nil, errs.NewInternalError()
	}

	return &CountResponse{
		Count: count,
	}, nil
}

// Helper functions
func generateSalt() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

func hashPassword(password, salt string) string {
	hash := sha256.Sum256([]byte(password + salt))
	return hex.EncodeToString(hash[:])
}
