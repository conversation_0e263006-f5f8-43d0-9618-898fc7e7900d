package gins

import (
	"digital-transformation-api/libs/apps"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/logs"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetIDBy<PERSON>ey gets or generates an ID for the given key from the request headers
func GetIDByKey(key string, ctx *gin.Context) string {
	id := ctx.GetHeader(key)
	if id == "" {
		id = uuid.NewString()
		ctx.Request.Header.Set(key, id)
	}

	return id
}

// getIDByKey is the internal version for backward compatibility
func getIDByKey(key string, ctx *gin.Context) string {
	return GetIDBy<PERSON>ey(key, ctx)
}

func NewCoreLogFromCtx(ctx *gin.Context) logger.CoreLogger {
	return logs.NewCoreLog(map[string]any{
		apps.TraceID: getIDBy<PERSON>ey(apps.TraceID, ctx),
		apps.SpanID:  getID<PERSON>y<PERSON><PERSON>(apps.SpanID, ctx),
	})
}

func NewLogFromCtx(ctx *gin.Context) logger.Logger {
	return logs.NewCoreLog(map[string]any{
		apps.TraceID: getID<PERSON>y<PERSON>ey(apps.TraceID, ctx),
		apps.SpanID:  getIDByKey(apps.SpanID, ctx),
	})
}
