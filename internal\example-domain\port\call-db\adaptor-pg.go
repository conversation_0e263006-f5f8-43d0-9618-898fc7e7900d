package calldb

import (
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gorms"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/logs"

	"gorm.io/gorm"
)

type adaptorPG struct {
	db *gorm.DB
}

func NewAdaptorPG(db *gorm.DB) Port {
	return &adaptorPG{
		db: db,
	}
}

func (a *adaptorPG) Execute(request *Request, rctx *contexts.RouteContext, l logger.Logger) (*Response, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	_ = tx

	return nil, nil
}
