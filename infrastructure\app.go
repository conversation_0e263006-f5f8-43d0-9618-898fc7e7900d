package infrastructure

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

var AppConfig AppConfigInfo

type AppConfigInfo struct {
	Name     string `mapstructure:"name"`
	Port     string `mapstructure:"port" validate:"required"`
	Env      string `mapstructure:"env" validate:"required"`
	TimeZone string `mapstructure:"timeZone"`
}

func InitAppConfig() {
	if err := viper.UnmarshalKey("app", &AppConfig); err != nil {
		panic(fmt.Errorf("failed to loaded app config: %v", err))
	}

	initTimeLocation()
}

func initTimeLocation() {
	if AppConfig.TimeZone == "" {
		AppConfig.TimeZone = "Asia/Bangkok"
	}

	loc, err := time.LoadLocation(AppConfig.TimeZone)
	if err != nil {
		panic(fmt.Errorf("failed to loaded time location %s: %v", AppConfig.TimeZone, err))
	}

	time.Local = loc
}
